#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
中文字体检测和修复脚本
"""

import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import os
import platform

def check_chinese_fonts():
    """检查系统中可用的中文字体"""
    print("=== 检查系统中的中文字体 ===")
    
    # 获取所有字体
    font_list = fm.findSystemFonts()
    chinese_fonts = []
    
    # 常见的中文字体名称
    chinese_font_names = [
        'SimHei', 'Microsoft YaHei', 'SimSun', 'KaiTi', 'FangSong',
        'Microsoft JhengHei', 'PingFang SC', 'Hiragino Sans GB',
        'STHeiti', 'STSong', 'STKaiti', 'STFangsong',
        'WenQuanYi Micro Hei', 'WenQuanYi Zen Hei', 'Noto Sans CJK'
    ]
    
    # 检查字体
    available_fonts = []
    for font_path in font_list:
        try:
            font_prop = fm.FontProperties(fname=font_path)
            font_name = font_prop.get_name()
            
            # 检查是否为中文字体
            for chinese_name in chinese_font_names:
                if chinese_name.lower() in font_name.lower():
                    available_fonts.append((font_name, font_path))
                    break
        except:
            continue
    
    print(f"找到 {len(available_fonts)} 个中文字体:")
    for font_name, font_path in available_fonts:
        print(f"  - {font_name}: {font_path}")
    
    return available_fonts

def test_chinese_display():
    """测试中文显示效果"""
    print("\n=== 测试中文显示效果 ===")
    
    # 尝试不同的字体设置
    font_configs = [
        ['SimHei'],
        ['Microsoft YaHei'],
        ['SimSun'],
        ['DejaVu Sans'],
        ['Arial Unicode MS'],
        ['SimHei', 'Microsoft YaHei', 'DejaVu Sans'],
    ]
    
    for i, fonts in enumerate(font_configs):
        try:
            plt.rcParams['font.sans-serif'] = fonts
            plt.rcParams['axes.unicode_minus'] = False
            
            # 创建测试图表
            fig, ax = plt.subplots(figsize=(8, 6))
            ax.plot([1, 2, 3], [1, 4, 2], 'o-')
            ax.set_title('中文标题测试 - 数据可视化')
            ax.set_xlabel('横轴标签（中文）')
            ax.set_ylabel('纵轴标签（中文）')
            
            # 保存测试图片
            test_filename = f'中文字体测试_{i+1}_{fonts[0]}.png'
            plt.savefig(test_filename, dpi=150, bbox_inches='tight')
            plt.close()
            
            print(f"✓ 字体配置 {fonts} 测试成功，保存为: {test_filename}")
            
        except Exception as e:
            print(f"✗ 字体配置 {fonts} 测试失败: {e}")

def download_chinese_font():
    """下载并安装中文字体"""
    print("\n=== 下载中文字体 ===")
    
    try:
        import requests
        import zipfile
        import shutil
        
        # 下载思源黑体
        font_url = "https://github.com/adobe-fonts/source-han-sans/releases/download/2.004R/SourceHanSansSC.zip"
        print("正在下载思源黑体...")
        
        response = requests.get(font_url, stream=True)
        if response.status_code == 200:
            with open("SourceHanSansSC.zip", "wb") as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            # 解压字体文件
            with zipfile.ZipFile("SourceHanSansSC.zip", 'r') as zip_ref:
                zip_ref.extractall("fonts")
            
            print("✓ 字体下载完成")
            
            # 查找字体文件
            font_files = []
            for root, dirs, files in os.walk("fonts"):
                for file in files:
                    if file.endswith('.otf') or file.endswith('.ttf'):
                        font_files.append(os.path.join(root, file))
            
            print(f"找到 {len(font_files)} 个字体文件")
            return font_files
            
        else:
            print("✗ 字体下载失败")
            return []
            
    except ImportError:
        print("需要安装 requests 库: pip install requests")
        return []
    except Exception as e:
        print(f"下载字体时出错: {e}")
        return []

def setup_chinese_fonts():
    """设置中文字体"""
    print("\n=== 设置中文字体 ===")
    
    # 检查系统
    system = platform.system()
    print(f"操作系统: {system}")
    
    # 根据系统设置字体
    if system == "Windows":
        fonts = ['SimHei', 'Microsoft YaHei', 'SimSun']
    elif system == "Darwin":  # macOS
        fonts = ['PingFang SC', 'Hiragino Sans GB', 'STHeiti']
    else:  # Linux
        fonts = ['WenQuanYi Micro Hei', 'WenQuanYi Zen Hei', 'Noto Sans CJK SC']
    
    # 添加通用字体作为备选
    fonts.extend(['DejaVu Sans', 'Arial Unicode MS', 'Liberation Sans'])
    
    print(f"推荐字体配置: {fonts}")
    
    # 设置matplotlib字体
    plt.rcParams['font.sans-serif'] = fonts
    plt.rcParams['axes.unicode_minus'] = False
    
    # 清除字体缓存
    try:
        fm._rebuild()
        print("✓ 字体缓存已清除")
    except:
        print("! 无法清除字体缓存，可能需要手动删除 ~/.matplotlib 目录")
    
    return fonts

def create_font_config_file():
    """创建字体配置文件"""
    config_content = '''#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
中文字体配置文件
在其他脚本中导入此文件以确保中文正确显示
"""

import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import platform

def setup_chinese_font():
    """设置中文字体"""
    system = platform.system()
    
    if system == "Windows":
        fonts = ['SimHei', 'Microsoft YaHei', 'SimSun', 'KaiTi']
    elif system == "Darwin":  # macOS
        fonts = ['PingFang SC', 'Hiragino Sans GB', 'STHeiti', 'STSong']
    else:  # Linux
        fonts = ['WenQuanYi Micro Hei', 'WenQuanYi Zen Hei', 'Noto Sans CJK SC', 'DejaVu Sans']
    
    # 添加备选字体
    fonts.extend(['Arial Unicode MS', 'Liberation Sans', 'FreeSans'])
    
    # 设置字体
    plt.rcParams['font.sans-serif'] = fonts
    plt.rcParams['axes.unicode_minus'] = False
    
    print(f"已设置中文字体: {fonts[0]}")
    return fonts[0]

# 自动执行设置
if __name__ == "__main__":
    setup_chinese_font()
else:
    setup_chinese_font()
'''
    
    with open('中文字体配置.py', 'w', encoding='utf-8') as f:
        f.write(config_content)
    
    print("✓ 已创建字体配置文件: 中文字体配置.py")

def main():
    """主函数"""
    print("中文字体检测和修复工具")
    print("=" * 50)
    
    # 1. 检查可用字体
    available_fonts = check_chinese_fonts()
    
    # 2. 设置字体
    fonts = setup_chinese_fonts()
    
    # 3. 测试中文显示
    test_chinese_display()
    
    # 4. 创建配置文件
    create_font_config_file()
    
    print("\n=== 修复完成 ===")
    print("建议:")
    print("1. 在可视化脚本开头添加: from 中文字体配置 import setup_chinese_font")
    print("2. 如果仍有问题，请安装 SimHei 或 Microsoft YaHei 字体")
    print("3. 重启Python环境以确保字体设置生效")

if __name__ == "__main__":
    main()
