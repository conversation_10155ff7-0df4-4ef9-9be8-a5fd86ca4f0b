#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
中文字体配置文件
在其他脚本中导入此文件以确保中文正确显示
"""

import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import platform

def setup_chinese_font():
    """设置中文字体"""
    import matplotlib

    system = platform.system()

    if system == "Windows":
        fonts = ['SimHei', 'Microsoft YaHei', 'SimSun', 'KaiTi']
    elif system == "Darwin":  # macOS
        fonts = ['PingFang SC', 'Hiragino Sans GB', 'STHeiti', 'STSong']
    else:  # Linux
        fonts = ['WenQuanYi Micro Hei', 'WenQuanYi Zen Hei', 'Noto Sans CJK SC', 'DejaVu Sans']

    # 添加备选字体
    fonts.extend(['Arial Unicode MS', 'Liberation Sans', 'FreeSans'])

    # 强制设置字体
    matplotlib.rcParams['font.sans-serif'] = fonts
    matplotlib.rcParams['axes.unicode_minus'] = False
    plt.rcParams['font.sans-serif'] = fonts
    plt.rcParams['axes.unicode_minus'] = False

    # 清除字体缓存
    try:
        fm._rebuild()
    except:
        pass

    print(f"已设置中文字体: {fonts[0]}")
    print(f"实际字体配置: {plt.rcParams['font.sans-serif']}")
    return fonts[0]

# 自动执行设置
if __name__ == "__main__":
    setup_chinese_font()
else:
    setup_chinese_font()
