#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
图表查看器 - 用于查看生成的可视化图表
"""

import os
import tkinter as tk
from tkinter import ttk
from PIL import Image, ImageTk
import glob

class ChartViewer:
    def __init__(self, root):
        self.root = root
        self.root.title("数据可视化图表查看器")
        self.root.geometry("1200x800")
        
        # 获取所有图表文件
        self.chart_files = glob.glob("图表*.png")
        self.chart_files.sort()
        
        self.current_index = 0
        
        # 创建界面
        self.create_widgets()
        
        # 显示第一张图表
        if self.chart_files:
            self.show_chart(0)
    
    def create_widgets(self):
        # 创建主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建控制框架
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 图表选择下拉框
        ttk.Label(control_frame, text="选择图表:").pack(side=tk.LEFT, padx=(0, 5))
        
        self.chart_var = tk.StringVar()
        self.chart_combo = ttk.Combobox(control_frame, textvariable=self.chart_var, 
                                       values=self.chart_files, state="readonly", width=40)
        self.chart_combo.pack(side=tk.LEFT, padx=(0, 10))
        self.chart_combo.bind("<<ComboboxSelected>>", self.on_chart_selected)
        
        # 导航按钮
        ttk.Button(control_frame, text="上一张", command=self.prev_chart).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="下一张", command=self.next_chart).pack(side=tk.LEFT, padx=5)
        
        # 图表显示框架
        self.image_frame = ttk.Frame(main_frame)
        self.image_frame.pack(fill=tk.BOTH, expand=True)
        
        # 图表标签
        self.image_label = ttk.Label(self.image_frame)
        self.image_label.pack(expand=True)
        
        # 信息标签
        self.info_label = ttk.Label(main_frame, text="", font=("Arial", 10))
        self.info_label.pack(pady=(10, 0))
    
    def show_chart(self, index):
        if 0 <= index < len(self.chart_files):
            self.current_index = index
            chart_file = self.chart_files[index]
            
            try:
                # 加载并显示图片
                image = Image.open(chart_file)
                
                # 调整图片大小以适应窗口
                window_width = self.root.winfo_width() - 50
                window_height = self.root.winfo_height() - 150
                
                if window_width > 100 and window_height > 100:
                    image.thumbnail((window_width, window_height), Image.Resampling.LANCZOS)
                
                photo = ImageTk.PhotoImage(image)
                self.image_label.configure(image=photo)
                self.image_label.image = photo  # 保持引用
                
                # 更新下拉框选择
                self.chart_var.set(chart_file)
                
                # 更新信息
                self.info_label.configure(text=f"图表 {index + 1}/{len(self.chart_files)}: {chart_file}")
                
            except Exception as e:
                self.info_label.configure(text=f"加载图表失败: {e}")
    
    def on_chart_selected(self, event):
        selected_chart = self.chart_var.get()
        if selected_chart in self.chart_files:
            index = self.chart_files.index(selected_chart)
            self.show_chart(index)
    
    def prev_chart(self):
        if self.current_index > 0:
            self.show_chart(self.current_index - 1)
    
    def next_chart(self):
        if self.current_index < len(self.chart_files) - 1:
            self.show_chart(self.current_index + 1)

def main():
    root = tk.Tk()
    app = ChartViewer(root)
    root.mainloop()

if __name__ == "__main__":
    main()
