#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
完整可视化图表生成器 - 生成9张专业图表
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import seaborn as sns
import warnings
import os
warnings.filterwarnings('ignore')

def setup_chinese_font():
    """设置中文字体"""
    font_paths = [
        r'C:\Windows\Fonts\simhei.ttf',
        r'C:\Windows\Fonts\SimHei.ttf', 
        r'C:\Windows\Fonts\msyh.ttc',
        r'C:\Windows\Fonts\simsun.ttc'
    ]
    
    font_prop = None
    for path in font_paths:
        if os.path.exists(path):
            try:
                font_prop = fm.FontProperties(fname=path)
                break
            except:
                continue
    
    # 设置全局字体
    chinese_fonts = ['SimHei', 'Microsoft YaHei', 'SimSun', 'KaiTi']
    plt.rcParams['font.sans-serif'] = chinese_fonts
    plt.rcParams['axes.unicode_minus'] = False
    
    import matplotlib
    matplotlib.rcParams['font.sans-serif'] = chinese_fonts
    matplotlib.rcParams['axes.unicode_minus'] = False
    
    return font_prop

def load_data():
    """加载数据"""
    house_data = pd.read_excel('数据可视化数据集-A/二手房数据.xlsx')
    restaurant_data = pd.read_excel('数据可视化数据集-A/某餐厅顾客消费记录.xlsx')
    marketing_data = pd.read_excel('数据可视化数据集-A/营销和产品销售表.xlsx')
    return house_data, restaurant_data, marketing_data

def create_house_charts(house_data, font_prop):
    """创建二手房相关图表"""
    
    # 图表1：散点图 - 面积与总价关系
    plt.figure(figsize=(12, 8))
    colors = plt.cm.Set3(np.linspace(0, 1, len(house_data['所在区'].unique())))
    
    for i, district in enumerate(house_data['所在区'].unique()):
        district_data = house_data[house_data['所在区'] == district]
        plt.scatter(district_data['面积（平方米）'], district_data['总价（万元）'], 
                   alpha=0.6, label=district, s=50, color=colors[i])
    
    plt.xlabel('面积（平方米）', fontproperties=font_prop, fontsize=14)
    plt.ylabel('总价（万元）', fontproperties=font_prop, fontsize=14)
    plt.title('北京二手房面积与总价关系散点图', fontproperties=font_prop, fontsize=16, fontweight='bold')
    plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left', prop=font_prop)
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig('图表1_二手房面积总价散点图.png', dpi=300, bbox_inches='tight')
    plt.show()
    print("✓ 图表1：散点图已生成")
    
    # 图表2：箱形图 - 各区域房价分布
    plt.figure(figsize=(14, 8))
    districts = house_data['所在区'].unique()
    box_data = [house_data[house_data['所在区'] == district]['单价（元/平方米）'] for district in districts]
    
    bp = plt.boxplot(box_data, labels=districts, patch_artist=True)
    colors = plt.cm.Set2(np.linspace(0, 1, len(bp['boxes'])))
    
    for patch, color in zip(bp['boxes'], colors):
        patch.set_facecolor(color)
        patch.set_alpha(0.7)
    
    plt.xlabel('所在区', fontproperties=font_prop, fontsize=14)
    plt.ylabel('单价（元/平方米）', fontproperties=font_prop, fontsize=14)
    plt.title('北京各区二手房单价分布箱形图', fontproperties=font_prop, fontsize=16, fontweight='bold')
    plt.xticks(rotation=45)
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig('图表2_各区房价分布箱形图.png', dpi=300, bbox_inches='tight')
    plt.show()
    print("✓ 图表2：箱形图已生成")
    
    # 图表3：热力图 - 房龄与户型价格关系
    plt.figure(figsize=(12, 8))
    
    # 创建房龄区间
    house_data['房龄区间'] = pd.cut(house_data['房龄（年）'], 
                                bins=[0, 5, 10, 20, 30, 50], 
                                labels=['0-5年', '6-10年', '11-20年', '21-30年', '30年以上'])
    
    # 创建透视表
    pivot_table = house_data.pivot_table(values='单价（元/平方米）', 
                                        index='房龄区间', 
                                        columns='户型（室）', 
                                        aggfunc='mean')
    
    sns.heatmap(pivot_table, annot=True, fmt='.0f', cmap='YlOrRd', 
                cbar_kws={'label': '平均单价（元/平方米）'})
    plt.title('房龄与户型的平均单价热力图', fontproperties=font_prop, fontsize=16, fontweight='bold')
    plt.xlabel('户型（室）', fontproperties=font_prop, fontsize=14)
    plt.ylabel('房龄区间', fontproperties=font_prop, fontsize=14)
    plt.tight_layout()
    plt.savefig('图表3_房龄户型价格热力图.png', dpi=300, bbox_inches='tight')
    plt.show()
    print("✓ 图表3：热力图已生成")

def create_restaurant_charts(restaurant_data, font_prop):
    """创建餐厅相关图表"""
    
    # 图表4：小提琴图 - 顾客类型消费分布
    plt.figure(figsize=(12, 8))
    
    customer_types = restaurant_data['顾客类型'].unique()
    violin_data = [restaurant_data[restaurant_data['顾客类型'] == ctype]['消费金额（元）'] 
                   for ctype in customer_types]
    
    violin_parts = plt.violinplot(violin_data, positions=range(len(customer_types)),
                                 showmeans=True, showmedians=True)
    
    colors = ['lightblue', 'lightcoral']
    for i, pc in enumerate(violin_parts['bodies']):
        pc.set_facecolor(colors[i])
        pc.set_alpha(0.7)
    
    plt.xticks(range(len(customer_types)), customer_types)
    plt.xlabel('顾客类型', fontproperties=font_prop, fontsize=14)
    plt.ylabel('消费金额（元）', fontproperties=font_prop, fontsize=14)
    plt.title('不同顾客类型消费金额分布小提琴图', fontproperties=font_prop, fontsize=16, fontweight='bold')
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig('图表4_顾客类型消费分布小提琴图.png', dpi=300, bbox_inches='tight')
    plt.show()
    print("✓ 图表4：小提琴图已生成")
    
    # 图表5：簇状柱形图 - 分店性别消费对比
    plt.figure(figsize=(12, 8))
    
    avg_consumption = restaurant_data.groupby(['分店', '性别'])['消费金额（元）'].mean().unstack()
    
    x = np.arange(len(avg_consumption.index))
    width = 0.35
    
    plt.bar(x - width/2, avg_consumption['女'], width, label='女性', 
            color='pink', alpha=0.8)
    plt.bar(x + width/2, avg_consumption['男'], width, label='男性', 
            color='lightblue', alpha=0.8)
    
    plt.xlabel('分店', fontproperties=font_prop, fontsize=14)
    plt.ylabel('平均消费金额（元）', fontproperties=font_prop, fontsize=14)
    plt.title('各分店不同性别顾客平均消费对比', fontproperties=font_prop, fontsize=16, fontweight='bold')
    plt.xticks(x, avg_consumption.index)
    plt.legend(prop=font_prop)
    plt.grid(True, alpha=0.3)
    
    # 添加数值标签
    for i, (male, female) in enumerate(zip(avg_consumption['男'], avg_consumption['女'])):
        plt.text(i - width/2, female + 5, f'{female:.0f}', ha='center', va='bottom')
        plt.text(i + width/2, male + 5, f'{male:.0f}', ha='center', va='bottom')
    
    plt.tight_layout()
    plt.savefig('图表5_分店性别消费对比簇状柱形图.png', dpi=300, bbox_inches='tight')
    plt.show()
    print("✓ 图表5：簇状柱形图已生成")
    
    # 图表6：气泡图 - 消费金额与满意度关系
    plt.figure(figsize=(12, 8))
    
    for i, ctype in enumerate(restaurant_data['顾客类型'].unique()):
        data = restaurant_data[restaurant_data['顾客类型'] == ctype]
        colors = ['red', 'blue'][i]
        plt.scatter(data['消费金额（元）'], data['顾客满意度'], 
                   s=100, alpha=0.6, label=ctype, color=colors)
    
    plt.xlabel('消费金额（元）', fontproperties=font_prop, fontsize=14)
    plt.ylabel('顾客满意度', fontproperties=font_prop, fontsize=14)
    plt.title('消费金额与顾客满意度关系气泡图', fontproperties=font_prop, fontsize=16, fontweight='bold')
    plt.legend(prop=font_prop)
    plt.grid(True, alpha=0.3)
    
    # 添加趋势线
    z = np.polyfit(restaurant_data['消费金额（元）'], restaurant_data['顾客满意度'], 1)
    p = np.poly1d(z)
    plt.plot(restaurant_data['消费金额（元）'], p(restaurant_data['消费金额（元）']), 
             "r--", alpha=0.8, linewidth=2, label='趋势线')
    
    plt.tight_layout()
    plt.savefig('图表6_消费满意度关系气泡图.png', dpi=300, bbox_inches='tight')
    plt.show()
    print("✓ 图表6：气泡图已生成")

def create_marketing_charts(marketing_data, font_prop):
    """创建营销相关图表"""
    
    # 图表7：折线图 - 营销费用与订单金额趋势
    fig, ax1 = plt.subplots(figsize=(14, 8))
    
    color = 'tab:red'
    ax1.set_xlabel('日期', fontproperties=font_prop, fontsize=14)
    ax1.set_ylabel('营销费用（元）', color=color, fontproperties=font_prop, fontsize=14)
    ax1.plot(marketing_data['日期'], marketing_data['营销费用（元）'], 
             color=color, linewidth=2, marker='o', label='营销费用')
    ax1.tick_params(axis='y', labelcolor=color)
    ax1.grid(True, alpha=0.3)
    
    ax2 = ax1.twinx()
    color = 'tab:blue'
    ax2.set_ylabel('订单金额（元）', color=color, fontproperties=font_prop, fontsize=14)
    ax2.plot(marketing_data['日期'], marketing_data['订单金额（元）'], 
             color=color, linewidth=2, marker='s', label='订单金额')
    ax2.tick_params(axis='y', labelcolor=color)
    
    plt.title('营销费用与订单金额时间趋势对比', fontproperties=font_prop, fontsize=16, fontweight='bold')
    
    # 合并图例
    lines1, labels1 = ax1.get_legend_handles_labels()
    lines2, labels2 = ax2.get_legend_handles_labels()
    ax1.legend(lines1 + lines2, labels1 + labels2, loc='upper left', prop=font_prop)
    
    plt.xticks(rotation=45)
    plt.tight_layout()
    plt.savefig('图表7_营销费用订单金额趋势折线图.png', dpi=300, bbox_inches='tight')
    plt.show()
    print("✓ 图表7：折线图已生成")
    
    # 图表8：面积图 - 营销指标变化
    plt.figure(figsize=(14, 8))
    
    indicators = ['展现量', '点击量', '加购数', '进店数']
    normalized_data = marketing_data[indicators].div(marketing_data[indicators].max())
    
    plt.stackplot(marketing_data['日期'], 
                  normalized_data['展现量'], 
                  normalized_data['点击量'], 
                  normalized_data['加购数'], 
                  normalized_data['进店数'],
                  labels=indicators,
                  alpha=0.8)
    
    plt.xlabel('日期', fontproperties=font_prop, fontsize=14)
    plt.ylabel('标准化指标值', fontproperties=font_prop, fontsize=14)
    plt.title('营销关键指标时间变化面积图', fontproperties=font_prop, fontsize=16, fontweight='bold')
    plt.legend(loc='upper left', prop=font_prop)
    plt.xticks(rotation=45)
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig('图表8_营销指标变化面积图.png', dpi=300, bbox_inches='tight')
    plt.show()
    print("✓ 图表8：面积图已生成")
    
    # 图表9：雷达图 - 营销效果评估
    fig, ax = plt.subplots(figsize=(10, 10), subplot_kw=dict(projection='polar'))
    
    last_week = marketing_data.tail(7)
    
    metrics = ['展现量', '点击量', '订单金额（元）', '加购数', '下单新客数', '访问页面数', '进店数']
    values = []
    for metric in metrics:
        normalized_value = (last_week[metric].mean() - marketing_data[metric].min()) / \
                          (marketing_data[metric].max() - marketing_data[metric].min())
        values.append(normalized_value)
    
    values += values[:1]
    metrics += metrics[:1]
    
    angles = np.linspace(0, 2 * np.pi, len(metrics), endpoint=True)
    
    ax.plot(angles, values, 'o-', linewidth=2, label='最近一周平均表现')
    ax.fill(angles, values, alpha=0.25)
    
    ax.set_xticks(angles[:-1])
    ax.set_xticklabels(metrics[:-1], fontproperties=font_prop)
    ax.set_ylim(0, 1)
    ax.set_title('营销效果综合评估雷达图', fontproperties=font_prop, fontsize=16, fontweight='bold', pad=20)
    ax.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0), prop=font_prop)
    
    plt.tight_layout()
    plt.savefig('图表9_营销效果评估雷达图.png', dpi=300, bbox_inches='tight')
    plt.show()
    print("✓ 图表9：雷达图已生成")

def main():
    """主函数"""
    print("=== 开始生成完整的9张可视化图表 ===")
    
    # 设置中文字体
    font_prop = setup_chinese_font()
    print("✓ 中文字体设置完成")
    
    # 加载数据
    house_data, restaurant_data, marketing_data = load_data()
    print("✓ 数据加载完成")
    
    # 生成图表
    print("\n--- 生成二手房相关图表 ---")
    create_house_charts(house_data, font_prop)
    
    print("\n--- 生成餐厅相关图表 ---")
    create_restaurant_charts(restaurant_data, font_prop)
    
    print("\n--- 生成营销相关图表 ---")
    create_marketing_charts(marketing_data, font_prop)
    
    print("\n=== 所有图表生成完成！===")
    print("共生成9张专业可视化图表，中文显示正常")

if __name__ == "__main__":
    main()
